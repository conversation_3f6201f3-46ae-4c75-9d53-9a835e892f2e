<?php

namespace Config;

use CodeIgniter\Router\RouteCollection;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();

// Auto-routing configuration
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
$routes->setAutoRoute(false); // Setting to false for security

// For improved auto-routing, uncomment the following line
// $routes->setAutoRoute(true);
// And ensure $autoRoutesImproved is set to true in app/Config/Feature.php

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');

// Authentication Routes
$routes->get('login', 'Home::login');
$routes->post('login', 'Home::login');
$routes->post('processLogin', 'Home::processLogin'); // Added route for processing login
$routes->post('dologin', 'Home::dologin');
$routes->get('logout', 'Home::logout');
$routes->get('about', 'Home::about');
$routes->get('findme/(:any)', 'Home::findme/$1');
$routes->post('gofindme', 'Home::gofindme');
$routes->post('open_profile', 'Home::open_profile');

// Dashboard Routes
$routes->get('dashboard', 'Admindash::index');

// Mapping Routes
$routes->get('mapping', 'Mapping::index');

// Dakoii Routes
$routes->group('dakoii', function($routes) {
    $routes->get('', 'DakoiiAuth::index');
    $routes->get('login', 'DakoiiAuth::login');
    $routes->post('login', 'DakoiiAuth::processLogin');
    $routes->get('logout', 'DakoiiAuth::logout');
    $routes->get('dashboard', 'DakoiiDashboard::index');
    $routes->post('dlogin', 'Dakoii::login');
    $routes->get('dlogout', 'Dakoii::logout');
    $routes->get('ddash', 'Dakoii::ddash');
    $routes->get('dopen_org/(:any)', 'Dakoii::open_org/$1');
    $routes->get('dlist_org', 'Dakoii::list_org');
    $routes->post('daddorg', 'Dakoii::addorg');
    $routes->post('deditorg', 'Dakoii::editorg');
    $routes->post('dadduser', 'Dakoii::adduser');
    $routes->post('daddadmin', 'Dakoii::create_admin');
    $routes->post('dakoii_set_license_status', 'Dakoii::dakoii_set_license_status');
    $routes->post('edit-admin', 'Dakoii::editAdmin');
    $routes->post('add-crop', 'Dakoii::addCrop');
    $routes->post('update-crop', 'Dakoii::updateCrop');
    $routes->post('add-pesticide', 'Dakoii::addPesticide');
    $routes->post('update-pesticide', 'Dakoii::updatePesticide');
    $routes->post('add-infection', 'Dakoii::addInfection');
    $routes->post('update-infection', 'Dakoii::updateInfection');
    $routes->post('add-fertilizer', 'Dakoii::addFertilizer');
    $routes->post('update-fertilizer', 'Dakoii::updateFertilizer');
    $routes->post('add-livestock', 'Dakoii::addLivestock');
    $routes->post('update-livestock', 'Dakoii::updateLivestock');
    $routes->get('organizations', 'Dakoii::organizations');
    $routes->post('edit-system-user', 'Dakoii::editSystemUser');

    // New RESTful Organization Routes using ID instead of orgcode
    $routes->group('organizations', function($routes) {
        $routes->get('/', 'DakoiiOrganizations::index');
        $routes->get('create', 'DakoiiOrganizations::create');
        $routes->post('store', 'DakoiiOrganizations::store');
        $routes->get('show/(:num)', 'DakoiiOrganizations::show/$1');
        $routes->get('edit/(:num)', 'DakoiiOrganizations::edit/$1');
        $routes->post('update/(:num)', 'DakoiiOrganizations::update/$1');
        $routes->post('update-license/(:num)', 'DakoiiOrganizations::updateLicense/$1');
    });
});

// Province & Location Management Routes
$routes->group('location-management', function($routes) {
    // Province routes
    $routes->get('provinces', 'Dakoii::provinces');
    $routes->post('add-province', 'Dakoii::addProvince');
    $routes->post('edit-province', 'Dakoii::editProvince');
    $routes->get('delete-province/(:num)', 'Dakoii::deleteProvince/$1');
    $routes->get('get-province/(:num)', 'Dakoii::getProvince/$1');

    // District routes
    $routes->get('get-districts/(:num)', 'Dakoii::getDistricts/$1');
    $routes->get('districts/(:num)', 'Dakoii::districts/$1');
    $routes->post('add-district', 'Dakoii::addDistrict');
    $routes->post('edit-district', 'Dakoii::editDistrict');
    $routes->get('delete-district/(:num)', 'Dakoii::deleteDistrict/$1');

    // LLG routes
    $routes->get('llgs/(:num)', 'Dakoii::llgs/$1');
    $routes->post('add-llg', 'Dakoii::addLLG');
    $routes->post('edit-llg', 'Dakoii::editLLG');
    $routes->get('delete-llg/(:num)', 'Dakoii::deleteLLG/$1');

    // Ward routes
    $routes->get('wards/(:num)', 'Dakoii::wards/$1');
    $routes->post('add-ward', 'Dakoii::addWard');
    $routes->post('edit-ward', 'Dakoii::editWard');
    $routes->get('delete-ward/(:num)', 'Dakoii::deleteWard/$1');
});

// User Management Routes
$routes->group('users', function($routes) {
    $routes->get('', 'Users::index');
    $routes->get('view/(:num)', 'Users::view/$1');
    $routes->post('add_user', 'Users::add_user');
    $routes->post('update_user', 'Users::update_user');
    $routes->get('delete_user/(:num)', 'Users::delete_user/$1');
    $routes->post('add_district_permission/(:num)', 'Users::add_district_permission/$1');
    $routes->get('set_default_district/(:num)', 'Users::set_default_district/$1');
    $routes->get('remove_district_permission/(:num)', 'Users::remove_district_permission/$1');
    $routes->post('check_fileno', 'Users::check_fileno');
});

// Groups Management Routes
$routes->group('groups', ['filter' => 'auth'], function ($routes) {
    $routes->get('/', 'Groups::index');
    $routes->post('add_group', 'Groups::add_group');
    $routes->post('update_group', 'Groups::update_group');
    $routes->get('delete_group/(:num)', 'Groups::delete_group/$1');
    $routes->get('get_groups', 'Groups::get_groups');
    $routes->get('get_group/(:num)', 'Groups::get_group/$1');
    $routes->get('get_group_path/(:num)', 'Groups::get_group_path/$1');
});

// Dashboards Routes
$routes->group('dashboards', function($routes) {
    $routes->get('farmers', 'Dashboards\Farmers_Report::index');
    $routes->get('farmers/profile/(:num)', 'Dashboards\Farmers_dashboard::profile/$1');
    $routes->get('crops/view/(:num)', 'Dashboards\Crops_Dashboard::view/$1');
});

// Specialized Dashboard Routes
$routes->get('crops-data', 'Dashboards\Crops_Dashboard::view');
$routes->get('crops-pesticides', 'Dashboards\CropsPesticides_Dashboard::index');
$routes->get('crops-harvests', 'Dashboards\CropsHarvests_Dashboard::index');
$routes->get('crops-markets', 'Dashboards\CropsMarket_Dashboard::index');
$routes->get('crops-diseases', 'Dashboards\CropsDiseases_Dashboard::index');
$routes->get('crops-fertilizers', 'Dashboards\CropsFertilizers_Dashboard::index');
$routes->get('crop-farm-blocks-dashboard', 'Dashboards\CropsBlock_Dashboard::dashboard_crops_blocks');
$routes->get('dashboards/crops-block/view/(:num)', 'Dashboards\CropsBlock_Dashboard::view/$1');

// Farmers Dashboard Routes
$routes->group('farmers', function($routes) {
    $routes->get('dashboard', 'Dashboards\Farmers_dashboard::index');
});

// Location Dashboard Routes
$routes->group('location', function($routes) {
    $routes->get('dashboard', 'Dashboards\Location_dashboard::index');
    $routes->post('dashboard/get-districts', 'Dashboards\Location_dashboard::getDistricts');
    $routes->post('dashboard/get-llgs', 'Dashboards\Location_dashboard::getLlgs');
    $routes->post('dashboard/get-wards', 'Dashboards\Location_dashboard::getWards');
    $routes->post('dashboard/get-dashboard-farmers-data', 'Dashboards\Location_dashboard::getDashboardFarmersData');
    $routes->post('dashboard/get-dashboard-crops-data', 'Dashboards\Location_dashboard::getDashboardCropsData');
    $routes->post('dashboard/get-dashboard-livestock-data', 'Dashboards\Location_dashboard::getDashboardLivestockData');
    $routes->post('dashboard/get-dashboard-market-data', 'Dashboards\Location_dashboard::getDashboardMarketData');
    $routes->post('dashboard/get-dashboard-livestock-market-data', 'Dashboards\Location_dashboard::getDashboardLivestockMarketData');
    $routes->post('dashboard/get-dashboard-fertilizer-data', 'Dashboards\Location_dashboard::getDashboardFertilizerData');
    $routes->post('dashboard/get-dashboard-pesticide-data', 'Dashboards\Location_dashboard::getDashboardPesticideData');
    $routes->post('dashboard/get-dashboard-data', 'Dashboards\Location_dashboard::getDashboardData');
});

// Selection Routes
$routes->group('selections', function($routes) {
    $routes->post('add-selection', 'Dakoii::addSelection');
    $routes->post('update-selection', 'Dakoii::updateSelection');
    $routes->get('delete-selection/(:num)', 'Dakoii::deleteSelection/$1');
});

// API Routes
$routes->group('api', function($routes) {
    $routes->post('get_districts', 'Api::get_districts');
    $routes->post('get_llgs', 'Api::get_llgs');
});

// Reports Routes
$routes->group('reports', ['filter' => 'auth'], function($routes) {
    $routes->get('farmers-report', 'Reports::farmersReport');
    $routes->get('farmer-profile/(:num)', 'Reports::farmerProfile/$1');
    $routes->get('crop-buyers', 'Reports::cropBuyers');
    $routes->get('farm-blocks', 'Reports::farmBlocksReport');
});

// Plans Management Routes
$routes->group('plans-management', ['filter' => 'auth'], function($routes) {
    $routes->get('', 'Plans::index');
    $routes->post('add', 'Plans::add');
    $routes->post('update', 'Plans::update');
    $routes->get('delete/(:num)', 'Plans::delete/$1');

    // Programs routes
    $routes->get('programs/(:num)', 'Plans::programs/$1');
    $routes->post('add-program', 'Plans::add_program');
    $routes->post('update-program', 'Plans::update_program');
    $routes->get('delete-program/(:num)', 'Plans::delete_program/$1');

    // Deliverables & Indicators routes
    $routes->get('programs/deliverables-indicators/(:num)', 'Plans::deliverables_indicators/$1');
    $routes->post('add-deliverable', 'Plans::add_deliverable');
    $routes->post('update-deliverable', 'Plans::update_deliverable');
    $routes->get('delete-deliverable/(:num)', 'Plans::delete_deliverable/$1');
    $routes->post('add-indicator', 'Plans::add_indicator');
    $routes->post('update-indicator', 'Plans::update_indicator');
    $routes->get('delete-indicator/(:num)', 'Plans::delete_indicator/$1');

    // KRAs & KPIs routes
    $routes->get('kras-kpis/(:num)', 'Plans::kras_kpis/$1');
    $routes->post('add-kra', 'Plans::add_kra');
    $routes->post('update-kra', 'Plans::update_kra');
    $routes->get('delete-kra/(:num)', 'Plans::delete_kra/$1');
    $routes->post('add-kpi', 'Plans::add_kpi');
    $routes->post('update-kpi', 'Plans::update_kpi');
    $routes->get('delete-kpi/(:num)', 'Plans::delete_kpi/$1');
});

// Projects Management Routes
$routes->group('projects', ['filter' => 'auth'], function($routes) {
    $routes->get('manage', 'Projects::manage');
    $routes->get('supervise', 'Projects::supervise');
    $routes->post('add-project', 'Projects::add_project');
    $routes->post('update-project', 'Projects::update_project');
    $routes->get('view/(:num)', 'Projects::view/$1');
    $routes->get('delete-project/(:num)', 'Projects::delete_project/$1');
    $routes->get('get-programs', 'Projects::getPrograms');
    $routes->get('phases/(:num)', 'Projects::phases/$1');
    $routes->post('add-phase', 'Projects::addPhase');
    $routes->post('update-phase', 'Projects::updatePhase');
    $routes->get('delete-phase/(:num)', 'Projects::deletePhase/$1');
    $routes->post('update-phase-status', 'Projects::updatePhaseStatus');
    $routes->get('get-llgs', 'Projects::getLLGs');
    $routes->get('get-indicators', 'Projects::getIndicators');
});

// Workplan Management Routes
$routes->group('workplans', ['filter' => 'auth'], function($routes) {
    $routes->get('manage', 'Workplans::manage', ['as' => 'manage_workplan']);
    $routes->get('supervise', 'Workplans::supervise', ['as' => 'supervise_workplan']);
    $routes->post('add-workplan', 'Workplans::add_workplan');
    $routes->post('update-workplan', 'Workplans::update_workplan');
    $routes->get('delete-workplan/(:num)', 'Workplans::delete_workplan/$1');
    $routes->get('get-llgs', 'Workplans::getLLGs');
    $routes->get('get-kpis', 'Workplans::getKPIs');
    $routes->get('manage-activities/(:num)', 'Workplans::manage_activities/$1');
    $routes->post('add-activity', 'Workplans::add_activity');
    $routes->post('update-activity', 'Workplans::update_activity');
    $routes->post('update-activity-status', 'Workplans::update_activity_status');
    $routes->get('delete-activity/(:num)', 'Workplans::delete_activity/$1');
    $routes->get('activities/manage-inputs', 'Workplans::manage_inputs');
    $routes->post('add-input', 'Workplans::add_input');
    $routes->post('update-input', 'Workplans::update_input');
    $routes->get('delete-input/(:num)', 'Workplans::delete_input/$1');
    $routes->get('activities/manage-infrastructure', 'Workplans::manage_infrastructure');
    $routes->post('add-infrastructure', 'Workplans::add_infrastructure');
    $routes->post('update-infrastructure', 'Workplans::update_infrastructure');
    $routes->get('delete-infrastructure/(:num)', 'Workplans::delete_infrastructure/$1');
    $routes->get('get-activities', 'Workplans::get_activities');

    // Training routes
    $routes->get('activities/manage-trainings', 'Workplans::manage_trainings');
    $routes->post('add-training', 'Workplans::add_training');
    $routes->post('update-training', 'Workplans::update_training');
    $routes->get('delete-training/(:num)', 'Workplans::delete_training/$1');

    // Training participants routes
    $routes->get('manage-training-participants/(:num)', 'Workplans::manage_training_participants/$1');
    $routes->post('add-participant', 'Workplans::add_participant');
    $routes->post('update-participant', 'Workplans::update_participant');
    $routes->get('delete-participant/(:num)', 'Workplans::delete_participant/$1');
});

// Weather Routes
$routes->get('weather', 'WeatherController::index');
$routes->get('weather/fetch', 'WeatherController::getWeatherData');

// Staff Module Routes
$routes->group('staff', ['namespace' => 'App\Controllers\Staff', 'filter' => 'auth'], function ($routes) {
    // Base Staff Routes
    $routes->get('/', 'Staff::index');
    $routes->get('switch_district/(:num)', 'Staff::switch_district/$1');
    $routes->get('data-entry', 'Staff::dataEntry');
    $routes->get('maps', 'Staff::maps');
    $routes->get('tasks', 'Staff::tasks');

    // Data Entry Routes
    $routes->post('submit-entry', 'Staff::submitEntry');
    $routes->post('upload-data', 'Staff::uploadData');

    // Maps Routes
    $routes->get('get-map-data', 'Staff::getMapData');

    // Tasks Routes
    $routes->post('add-task', 'Staff::addTask');
    $routes->post('update-task', 'Staff::updateTask');
    $routes->get('delete-task/(:num)', 'Staff::deleteTask/$1');

    // Buyer Routes
    $routes->get('buyers', 'Staff::buyers');
    $routes->post('add-buyer', 'Staff::addBuyer');
    $routes->post('update-buyer', 'Staff::updateBuyer');

    // Farmer Routes
    $routes->get('farmers', 'FarmerController::index');
    $routes->get('farmers/create', 'FarmerController::create');
    $routes->post('farmers', 'FarmerController::store');
    $routes->get('farmers/edit/(:num)', 'FarmerController::edit/$1');
    $routes->post('farmers/update/(:num)', 'FarmerController::update/$1');
    $routes->get('farmers/delete/(:num)', 'FarmerController::delete/$1');
    $routes->get('farmers/view/(:num)', 'FarmerController::view/$1');
    $routes->get('farmers/getLLGs', 'FarmerController::getLLGs');
    $routes->get('farmers/getWards', 'FarmerController::getWards');
    $routes->get('farmers/getWardsByLlg/(:num)', 'FarmerController::getWardsByLlg/$1');

    // Farmer Children Routes
    $routes->post('farmers/add-child', 'FarmerController::addChild');
    $routes->post('farmers/update-child', 'FarmerController::updateChild');
    $routes->get('farmers/delete-child/(:num)', 'FarmerController::deleteChild/$1');

    // Farm Block Routes
    $routes->group('farms', function($routes) {
        // Basic farm block routes
        $routes->get('/', 'Staff_FarmsController::view');
        $routes->get('view', 'Staff_FarmsController::view');
        $routes->get('farm_blocks/(:num)', 'Staff_FarmsController::farm_blocks/$1');
        $routes->get('view-block/(:num)', 'Staff_Farms::viewBlock/$1');
        $routes->post('update-block/(:num)', 'Staff_Farms::updateBlock/$1');
        $routes->post('delete-block/(:num)', 'Staff_Farms::deleteBlock/$1');
        $routes->post('delete-block-data/(:num)', 'Staff_Farms::delete_block_data/$1');
        $routes->get('get-block-data/(:num)', 'Staff_Farms::get_block_data/$1');
        $routes->post('update-block-data', 'Staff_Farms::update_block_data');
        $routes->get('maps', 'Staff_FarmsController::maps');
        $routes->get('get_farm_block/(:num)', 'Staff_Farms::get_farm_block/$1');

        // AJAX routes for locations
        $routes->post('get_llgs', 'Staff_FarmsController::get_llgs');
        $routes->post('get_wards', 'Staff_FarmsController::get_wards');
        $routes->post('search_farmers', 'Staff_Farms::search_farmers');
        $routes->post('get_farm_blocks', 'Staff_Farms::get_farm_blocks');

        // CRUD routes for farm blocks
        $routes->post('add_farm_block', 'Staff_FarmsController::add_farm_block');
        $routes->post('update_farm_block', 'Staff_FarmsController::update_farm_block');
        $routes->get('delete_farm_block/(:num)', 'Staff_FarmsController::delete_farm_block/$1');
    });

    // Crop Management Routes (both old and new patterns for compatibility)
    $routes->group('farms', function($routes) {
        // Legacy crop block routes
        $routes->get('crops-blocks', 'StaffCropsData::view_crop_blocks');
        $routes->get('view-crops-data/(:num)', 'StaffCropsData::view_crops_data/$1');
        $routes->post('add-crops-data', 'StaffCropsData::add_crops_data');
        $routes->post('update-crops-data', 'StaffCropsData::update_crops_data');
        $routes->get('delete-block-data/(:num)', 'StaffCropsData::delete_block_data/$1');
        $routes->get('delete-crops-data/(:num)', 'StaffCropsData::delete_crops_data/$1');

        // Legacy fertilizer routes
        $routes->get('fertilizer-data', 'StaffFertilizer::fertilizer_data');
        $routes->get('fertilizer_data', 'StaffFertilizer::fertilizer_data');
        $routes->get('view-fertilizer-data/(:num)', 'StaffFertilizer::view_fertilizer_data/$1');
        $routes->post('add-fertilizer-data', 'StaffFertilizer::add_fertilizer_data');
        $routes->post('update-fertilizer-data', 'StaffFertilizer::update_fertilizer_data');
        $routes->get('delete-fertilizer-data/(:num)', 'StaffFertilizer::delete_fertilizer_data/$1');

        // Legacy pesticides routes
        $routes->get('pesticides-data', 'StaffPesticides::pesticides_data');
        $routes->get('pesticides_data', 'StaffPesticides::pesticides_data');
        $routes->get('view-pesticides-data/(:num)', 'StaffPesticides::view_pesticides_data/$1');
        $routes->post('add-pesticides-data', 'StaffPesticides::add_pesticides_data');
        $routes->post('update-pesticides-data', 'StaffPesticides::update_pesticides_data');
        $routes->get('delete-pesticides-data/(:num)', 'StaffPesticides::delete_pesticides_data/$1');

        // Legacy harvest routes
        $routes->get('harvest-data', 'StaffHarvest::harvest_data');
        $routes->get('harvest_data', 'StaffHarvest::harvest_data');
        $routes->get('view-harvest-data/(:num)', 'StaffHarvest::view_harvest_data/$1');
        $routes->post('add-harvest-data', 'StaffHarvest::add_harvest_data');
        $routes->post('update-harvest-data', 'StaffHarvest::update_harvest_data');
        $routes->get('delete-harvest-data/(:num)', 'StaffHarvest::delete_harvest_data/$1');

        // Legacy marketing routes
        $routes->get('marketing-data', 'StaffMarket::market_data');
        $routes->get('marketing_data', 'StaffMarket::market_data');
        $routes->get('view-market-data/(:num)', 'StaffMarket::view_market_data/$1');
        $routes->post('add-market-data', 'StaffMarket::add_market_data');
        $routes->post('update-market-data', 'StaffMarket::update_market_data');
        $routes->get('delete-market-data/(:num)', 'StaffMarket::delete_market_data/$1');
        $routes->get('edit-market-data/(:num)', 'StaffMarket::edit_market_data/$1');

        // Legacy disease routes
        $routes->get('diseases_data', 'StaffDisease::disease_data');
        $routes->get('view-diseases-data/(:num)', 'StaffDisease::view_disease_data/$1');
        $routes->post('add-diseases-data', 'StaffDisease::add_disease_data');
        $routes->post('update-diseases-data', 'StaffDisease::update_disease_data');
        $routes->get('delete-diseases-data/(:num)', 'StaffDisease::delete_disease_data/$1');
    });

    // New organized crop routes
    $routes->group('crops', function($routes) {
        // Crop Blocks Routes
        $routes->get('blocks', 'StaffCropsData::view_crop_blocks');
        $routes->get('blocks/view/(:num)', 'StaffCropsData::view_crops_data/$1');
        $routes->post('blocks/add', 'StaffCropsData::add_crops_data');
        $routes->post('blocks/update', 'StaffCropsData::update_crops_data');
        $routes->get('blocks/delete/(:num)', 'StaffCropsData::delete_block_data/$1');
        $routes->get('blocks/delete-data/(:num)', 'StaffCropsData::delete_crops_data/$1');

        // Fertilizer Routes
        $routes->get('fertilizer', 'StaffFertilizer::fertilizer_data');
        $routes->get('fertilizer/view/(:num)', 'StaffFertilizer::view_fertilizer_data/$1');
        $routes->post('fertilizer/add', 'StaffFertilizer::add_fertilizer_data');
        $routes->post('fertilizer/update', 'StaffFertilizer::update_fertilizer_data');
        $routes->get('fertilizer/delete/(:num)', 'StaffFertilizer::delete_fertilizer_data/$1');

        // Pesticides Routes
        $routes->get('pesticides', 'StaffPesticides::pesticides_data');
        $routes->get('pesticides/view/(:num)', 'StaffPesticides::view_pesticides_data/$1');
        $routes->post('pesticides/add', 'StaffPesticides::add_pesticides_data');
        $routes->post('pesticides/update', 'StaffPesticides::update_pesticides_data');
        $routes->get('pesticides/delete/(:num)', 'StaffPesticides::delete_pesticides_data/$1');

        // Harvest Routes
        $routes->get('harvest', 'StaffHarvest::harvest_data');
        $routes->get('harvest/view/(:num)', 'StaffHarvest::view_harvest_data/$1');
        $routes->post('harvest/add', 'StaffHarvest::add_harvest_data');
        $routes->post('harvest/update', 'StaffHarvest::update_harvest_data');
        $routes->get('harvest/delete/(:num)', 'StaffHarvest::delete_harvest_data/$1');

        // Marketing Routes
        $routes->get('marketing', 'StaffMarket::market_data');
        $routes->get('marketing/view/(:num)', 'StaffMarket::view_market_data/$1');
        $routes->post('marketing/add', 'StaffMarket::add_market_data');
        $routes->post('marketing/update', 'StaffMarket::update_market_data');
        $routes->get('marketing/delete/(:num)', 'StaffMarket::delete_market_data/$1');

        // Disease Routes
        $routes->get('diseases', 'StaffDisease::disease_data');
        $routes->get('diseases/view/(:num)', 'StaffDisease::view_disease_data/$1');
        $routes->post('diseases/add', 'StaffDisease::add_disease_data');
        $routes->post('diseases/update', 'StaffDisease::update_disease_data');
        $routes->get('diseases/delete/(:num)', 'StaffDisease::delete_disease_data/$1');
    });

    // Livestock Module Routes
    $routes->group('livestock', function($routes) {
        // Livestock Farm Blocks Routes
        $routes->get('farm-blocks', 'Staff_Livestock::farm_blocks');
        $routes->get('get-farm-blocks', 'Staff_Livestock::get_farm_blocks');
        $routes->post('add-farm-block', 'Staff_Livestock::add_farm_block');
        $routes->post('update-farm-block', 'Staff_Livestock::update_farm_block');
        $routes->post('delete-farm-block', 'Staff_Livestock::delete_farm_block');
        $routes->post('get-llgs', 'Staff_Livestock::get_llgs');
        $routes->post('get-wards', 'Staff_Livestock::get_wards');
        $routes->get('farm-data', 'Staff_Livestock::farm_data');
        $routes->get('view-farm-data/(:num)', 'Staff_Livestock::view_farm_data/$1');

        // Livestock Farm Data Routes
        $routes->post('add-livestock-data', 'Staff_Livestock::add_livestock_data');
        $routes->post('update-livestock-data', 'Staff_Livestock::update_livestock_data');
        $routes->post('delete-livestock-data', 'Staff_Livestock::delete_livestock_data');
    });

    // Disease Management
    $routes->get('diseases_data', 'StaffDisease::disease_data');
    $routes->get('view-diseases-data/(:num)', 'StaffDisease::view_disease_data/$1');
    $routes->post('add-diseases-data', 'StaffDisease::add_disease_data');
    $routes->post('update-diseases-data', 'StaffDisease::update_disease_data');
    $routes->get('delete-diseases-data/(:num)', 'StaffDisease::delete_disease_data/$1');

    // Staff Reports Routes
    $routes->group('reports', function($routes) {
        $routes->get('farmers', 'Staff_Reports::farmers');
        $routes->get('farmer_profile/(:num)', 'Staff_Reports::farmer_profile/$1');
        $routes->get('crops', 'Staff_Reports::crops');
        $routes->get('blocks', 'Staff_Reports::blocks');
        $routes->get('block_profile/(:num)', 'Staff_Reports::block_profile/$1');
        $routes->get('diseases', 'Staff_Reports::diseases');
        $routes->get('fertilizer', 'Staff_Reports::fertilizer');
        $routes->get('pesticides', 'Staff_Reports::pesticides');
        $routes->get('harvests', 'Staff_Reports::harvests');
        $routes->get('marketing', 'Staff_Reports::marketing');
        $routes->get('livestock-blocks', 'Staff_Reports::livestock_blocks');
        $routes->get('livestock-data', 'Staff_Reports::livestock_data');
    });

    // Staff Workplan routes
    $routes->group('workplan', function($routes) {
        $routes->get('manage', 'Workplan::manage');
    });
});

// Farm Block Files routes
$routes->post('staff/farms/upload_farm_block_file', 'Staff\Staff_FarmsController::upload_farm_block_file');
$routes->get('staff/farms/get_farm_block_files/(:num)', 'Staff\Staff_FarmsController::get_farm_block_files/$1');
$routes->post('staff/farms/delete_farm_block_file/(:num)', 'Staff\Staff_FarmsController::delete_farm_block_file/$1');
$routes->post('staff/farms/update_farm_block_file', 'Staff\Staff_FarmsController::update_farm_block_file');

// Exercises Routes
$routes->group('exercises', function($routes) {
    $routes->get('', 'ExercisesController::index');
    $routes->get('create', 'ExercisesController::create');
    $routes->post('store', 'ExercisesController::store');
    $routes->get('view/(:num)', 'ExercisesController::view/$1');
    $routes->get('edit/(:num)', 'ExercisesController::edit/$1');
    $routes->post('update/(:num)', 'ExercisesController::update/$1');
    $routes->post('updateStatus/(:num)', 'ExercisesController::updateStatus/$1');
    $routes->post('delete/(:num)', 'ExercisesController::delete/$1');
});

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (\is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
