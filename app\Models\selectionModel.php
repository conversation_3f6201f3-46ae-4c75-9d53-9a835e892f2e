<?php namespace App\Models;

use CodeIgniter\Model;

class SelectionModel extends Model
{
    protected $table = 'selection';
    protected $primaryKey = 'id';
    protected $allowedFields = ['box', 'value', 'item', 'hints', 'icons'];

    // Add these helper methods for better crop handling
    public function getCrops()
    {
        return $this->where('box', 'crops')
                    ->findAll();
    }

    public function getCropById($id)
    {
        return $this->find($id);
    }

    public function getCropName($id)
    {
        $crop = $this->getCropById($id);
        return $crop ? $crop['item'] : null;
    }

    public function getCropValue($id)
    {
        $crop = $this->getCropById($id);
        return $crop ? $crop['value'] : null;
    }

    // Method to handle both old (value) and new (id) crop references
    public function getCropInfo($identifier)
    {
        return $this->where('box', 'crops')
                    ->groupStart()
                        ->where('id', $identifier)
                        ->orWhere('value', $identifier)
                    ->groupEnd()
                    ->first();
    }

    // Method to migrate old value references to new id references
    public function getIdFromValue($value)
    {
        $crop = $this->where('box', 'crops')
                     ->where('value', $value)
                     ->first();
        return $crop ? $crop['id'] : null;
    }

    // Method to get value from id (for backward compatibility)
    public function getValueFromId($id)
    {
        $crop = $this->find($id);
        return $crop ? $crop['value'] : null;
    }
}
