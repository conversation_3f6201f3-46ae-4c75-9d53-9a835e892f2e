<?php

namespace App\Controllers;

use App\Models\UsersModel;
use App\Models\orgModel;

/**
 * Dakoii System Administrators Controller
 * 
 * Manages system administrators (users with role 'admin') from the main users table
 * These are organization administrators, not Dakoii portal users
 */
class DakoiiSystemAdmins extends BaseController
{
    protected $usersModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->orgModel = new orgModel();
        $this->session = \Config\Services::session();

        // Load helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display system administrators list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "System Administrators";
        $data['menu'] = "system_admins";
        
        // Get all admin users with organization details
        // Note: Handle data type mismatch between dakoii_org.orgcode (varchar) and users.orgcode (int)
        $data['admins'] = $this->usersModel->select('users.*, dakoii_org.name as org_name')
                                          ->join('dakoii_org', 'dakoii_org.orgcode = CAST(users.orgcode AS CHAR)', 'left')
                                          ->where('users.role', 'admin')
                                          ->orderBy('users.created_at', 'DESC')
                                          ->findAll();

        return view('dakoii/dakoii_system_admins_index', $data);
    }

    /**
     * Show create system administrator form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create System Administrator";
        $data['menu'] = "system_admins";
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_system_admins_create', $data);
    }

    /**
     * Store new system administrator
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Validate input
        $rules = [
            'orgcode' => 'required',
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[users.username]',
            'password' => 'required|min_length[6]',
            'email' => 'permit_empty|valid_email|max_length[500]',
            'phone' => 'permit_empty|max_length[200]',
            'position' => 'permit_empty|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please check your input: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/system-admins/create')->withInput();
        }

        // Get organization details
        $org = $this->orgModel->where('orgcode', $this->request->getPost('orgcode'))->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization selected');
            return redirect()->to('dakoii/system-admins/create')->withInput();
        }

        $data = [
            'org_id' => $org['id'],
            'orgcode' => (int)$this->request->getPost('orgcode'), // Cast to int to match users table
            'fileno' => $this->request->getPost('fileno') ?: '',
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => 'admin', // Fixed as admin
            'position' => trim($this->request->getPost('position')),
            'phone' => trim($this->request->getPost('phone')),
            'email' => trim($this->request->getPost('email')),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'status' => $this->request->getPost('is_active') ? 1 : 0,
            'created_by' => $this->session->get('dakoii_username')
        ];

        if ($this->usersModel->insert($data)) {
            session()->setFlashdata('success', 'System administrator "' . $data['name'] . '" created successfully!');
            return redirect()->to('dakoii/system-admins');
        } else {
            session()->setFlashdata('error', 'Failed to create system administrator. Please try again.');
            return redirect()->to('dakoii/system-admins/create')->withInput();
        }
    }

    /**
     * Show system administrator details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->select('users.*, dakoii_org.name as org_name')
                                 ->join('dakoii_org', 'dakoii_org.orgcode = CAST(users.orgcode AS CHAR)', 'left')
                                 ->where('users.id', $id)
                                 ->where('users.role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        $data['title'] = "System Administrator - " . $admin['name'];
        $data['menu'] = "system_admins";
        $data['admin'] = $admin;

        return view('dakoii/dakoii_system_admins_show', $data);
    }

    /**
     * Show edit system administrator form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->where('id', $id)
                                 ->where('role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        $data['title'] = "Edit System Administrator - " . $admin['name'];
        $data['menu'] = "system_admins";
        $data['admin'] = $admin;
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_system_admins_edit', $data);
    }

    /**
     * Update system administrator
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->where('id', $id)
                                 ->where('role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        // Validate input
        $rules = [
            'orgcode' => 'required',
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[users.username,id,' . $id . ']',
            'email' => 'permit_empty|valid_email|max_length[500]',
            'phone' => 'permit_empty|max_length[200]',
            'position' => 'permit_empty|max_length[255]'
        ];

        // Only validate password if provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please check your input: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }

        // Get organization details
        $org = $this->orgModel->where('orgcode', $this->request->getPost('orgcode'))->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization selected');
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }

        $data = [
            'org_id' => $org['id'],
            'orgcode' => (int)$this->request->getPost('orgcode'), // Cast to int to match users table
            'fileno' => $this->request->getPost('fileno') ?: '',
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'position' => trim($this->request->getPost('position')),
            'phone' => trim($this->request->getPost('phone')),
            'email' => trim($this->request->getPost('email')),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'status' => $this->request->getPost('is_active') ? 1 : 0,
            'updated_by' => $this->session->get('dakoii_username')
        ];

        // Update password only if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($this->usersModel->update($id, $data)) {
            session()->setFlashdata('success', 'System administrator "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/system-admins/show/' . $id);
        } else {
            session()->setFlashdata('error', 'Failed to update system administrator. Please try again.');
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }
    }

    /**
     * Delete system administrator
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->where('id', $id)
                                 ->where('role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        if ($this->usersModel->delete($id)) {
            session()->setFlashdata('success', 'System administrator "' . $admin['name'] . '" deleted successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to delete system administrator. Please try again.');
        }

        return redirect()->to('dakoii/system-admins');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}
