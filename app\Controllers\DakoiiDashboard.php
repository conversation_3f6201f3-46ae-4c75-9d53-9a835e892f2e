<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\AdxProvinceModel;
use App\Models\UsersModel;
use App\Models\SelectionModel;

class DakoiiDashboard extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;
    public $selectionModel;
    public $cropsModel;
    public $fertilizersModel;
    public $pesticidesModel;
    public $educationModel;
    public $infectionsModel;
    public $livestockModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Initialize models
        $this->dusersModel = new DakoiiUsersModel();
        $this->usersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new \App\Models\AdxDistrictModel();
        $this->llgModel = new \App\Models\AdxLlgModel();
        $this->wardModel = new \App\Models\AdxWardModel();
        $this->selectionModel = new \App\Models\SelectionModel();
        $this->cropsModel = new \App\Models\CropsModel();
        $this->fertilizersModel = new \App\Models\FertilizersModel();
        $this->pesticidesModel = new \App\Models\PesticidesModel();
        $this->educationModel = new \App\Models\EducationModel();
        $this->infectionsModel = new \App\Models\InfectionsModel();
        $this->livestockModel = new \App\Models\LivestockModel();
    }

    /**
     * Display dashboard
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Dakoii Dashboard";
        $data['menu'] = "dashboard";

        // Get all data for dashboard
        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->orderBy('id', 'DESC')->findAll();
        $data['selections'] = $this->selectionModel->orderBy('id', 'DESC')->findAll();

        // Location statistics
        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        // Province statistics
        $data['province_stats'] = $this->getProvinceStats();

        // Data management statistics
        $data['crops'] = $this->cropsModel->findAll();
        $data['fertilizers'] = $this->fertilizersModel->findAll();
        $data['pesticides'] = $this->pesticidesModel->findAll();
        $data['education'] = $this->educationModel->findAll();
        $data['infections'] = $this->infectionsModel->findAll();
        $data['livestock'] = $this->livestockModel->findAll();

        // Calculate additional statistics
        $data['stats'] = $this->calculateDashboardStats($data);

        return view('dakoii/dakoii_dashboard', $data);
    }

    /**
     * Get province statistics
     */
    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->countAllResults();
            $llgs = $this->llgModel->where('province_id', $province['id'])->countAllResults();
            $wards = $this->wardModel->where('province_id', $province['id'])->countAllResults();

            $stats[] = [
                'province' => $province,
                'districts_count' => $districts,
                'llgs_count' => $llgs,
                'wards_count' => $wards
            ];
        }

        return $stats;
    }

    /**
     * Calculate dashboard statistics
     */
    private function calculateDashboardStats($data)
    {
        $stats = [];

        // Organization statistics
        $stats['organizations'] = [
            'total' => count($data['org']),
            'active' => count(array_filter($data['org'], fn($o) => $o['is_active'] == 1)),
            'inactive' => count(array_filter($data['org'], fn($o) => $o['is_active'] == 0)),
            'paid' => count(array_filter($data['org'], fn($o) => $o['license_status'] == 'paid')),
            'trial' => count(array_filter($data['org'], fn($o) => $o['license_status'] == 'trial'))
        ];

        // User statistics
        $stats['users'] = [
            'total' => count($data['dusers']),
            'active' => count(array_filter($data['dusers'], fn($u) => $u['is_active'] == 1)),
            'inactive' => count(array_filter($data['dusers'], fn($u) => $u['is_active'] == 0)),
            'admins' => count(array_filter($data['dusers'], fn($u) => $u['role'] == 'admin')),
            'moderators' => count(array_filter($data['dusers'], fn($u) => $u['role'] == 'moderator')),
            'users' => count(array_filter($data['dusers'], fn($u) => $u['role'] == 'user'))
        ];

        // Data management statistics
        $stats['data'] = [
            'crops' => count($data['crops']),
            'fertilizers' => count($data['fertilizers']),
            'pesticides' => count($data['pesticides']),
            'infections' => count($data['infections']),
            'livestock' => count($data['livestock']),
            'education' => count($data['education'])
        ];

        // Location statistics
        $stats['locations'] = [
            'provinces' => $data['provinces_count'],
            'districts' => $data['districts_count'],
            'llgs' => $data['llgs_count'],
            'wards' => $data['wards_count']
        ];

        return $stats;
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }
}
